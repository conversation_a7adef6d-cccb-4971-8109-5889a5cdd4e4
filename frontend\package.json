{"name": "collegelms", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@gsap/react": "^2.1.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.1.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "gsap": "^3.13.0", "lucide-react": "^0.475.0", "prop-types": "^15.8.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.7.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/node": "^22.13.1", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.5.2", "tailwindcss": "^3.4.1", "vite": "^6.1.0"}}